"""
Test suite for RESTful standardized APIs.

This module tests the standardized API endpoints that have been refactored
to follow RESTful principles and consistent response formats.
"""

import pytest
import asyncio
from fastapi.testclient import TestClient
from unittest.mock import patch, AsyncMock
import json

# Import the FastAPI app
from backend.app import app

client = TestClient(app)


class TestDahanghaiRateAPI:
    """Test cases for /basic/dahanghai/rate endpoint"""
    
    def test_dahanghai_rate_success(self):
        """Test successful dahanghai rate retrieval"""
        with patch('backend.app.get_user_mid', return_value='401315430'), \
             patch('backend.app.calculate_dahanghai_rate_by_mid', new_callable=AsyncMock, return_value='10.5%'):
            
            response = client.get("/basic/dahanghai/rate?vtuber=星瞳&recent=90")
            
            assert response.status_code == 200
            data = response.json()
            
            # Check response structure
            assert "code" in data
            assert "message" in data
            assert "data" in data
            
            assert data["code"] == 200
            assert data["message"] == "Dahanghai growth rate retrieved successfully"
            
            # Check data structure
            response_data = data["data"]
            assert response_data["vtuber_name"] == "星瞳"
            assert response_data["mid"] == "401315430"
            assert response_data["growth_rate"] == "10.5%"
            assert response_data["period_days"] == 90
            assert "timestamp" in response_data
    
    def test_dahanghai_rate_invalid_recent(self):
        """Test dahanghai rate with invalid recent parameter"""
        response = client.get("/basic/dahanghai/rate?vtuber=星瞳&recent=-2")
        
        assert response.status_code == 400
        data = response.json()
        assert data["detail"]["code"] == 400
        assert "recent" in data["detail"]["message"]
    
    def test_dahanghai_rate_empty_vtuber(self):
        """Test dahanghai rate with empty vtuber parameter"""
        response = client.get("/basic/dahanghai/rate?vtuber=&recent=90")
        
        assert response.status_code == 400
        data = response.json()
        assert data["detail"]["code"] == 400
        assert "vtuber" in data["detail"]["message"]
    
    def test_dahanghai_rate_vtuber_not_found(self):
        """Test dahanghai rate with non-existent vtuber"""
        with patch('backend.app.get_user_mid', side_effect=Exception("Character not found")):
            response = client.get("/basic/dahanghai/rate?vtuber=不存在的主播&recent=90")
            
            assert response.status_code == 404
            data = response.json()
            assert data["detail"]["code"] == 404
            assert "not found" in data["detail"]["message"]
    
    def test_dahanghai_rate_default_parameters(self):
        """Test dahanghai rate with default parameters"""
        with patch('backend.app.get_user_mid', return_value='401315430'), \
             patch('backend.app.calculate_dahanghai_rate_by_mid', new_callable=AsyncMock, return_value='5.2%'):
            
            response = client.get("/basic/dahanghai/rate")
            
            assert response.status_code == 200
            data = response.json()
            
            response_data = data["data"]
            assert response_data["vtuber_name"] == "星瞳"  # Default value
            assert response_data["period_days"] == 90  # Default value


class TestFollowerRateAPI:
    """Test cases for /basic/follower/rate endpoint"""
    
    def test_follower_rate_success(self):
        """Test successful follower rate retrieval"""
        with patch('backend.app.get_user_mid', return_value='401315430'), \
             patch('backend.app.calculate_follower_rate_by_mid', new_callable=AsyncMock, return_value='-0.7%'):
            
            response = client.get("/basic/follower/rate?vtuber=星瞳&recent=30")
            
            assert response.status_code == 200
            data = response.json()
            
            assert data["code"] == 200
            assert data["message"] == "Follower growth rate retrieved successfully"
            
            response_data = data["data"]
            assert response_data["vtuber_name"] == "星瞳"
            assert response_data["mid"] == "401315430"
            assert response_data["growth_rate"] == "-0.7%"
            assert response_data["period_days"] == 30
    
    def test_follower_rate_invalid_recent(self):
        """Test follower rate with invalid recent parameter"""
        response = client.get("/basic/follower/rate?vtuber=星瞳&recent=-5")
        
        assert response.status_code == 400
        data = response.json()
        assert data["detail"]["code"] == 400
    
    def test_follower_rate_server_error(self):
        """Test follower rate with server error"""
        with patch('backend.app.get_user_mid', return_value='401315430'), \
             patch('backend.app.calculate_follower_rate_by_mid', new_callable=AsyncMock, side_effect=Exception("Database error")):
            
            response = client.get("/basic/follower/rate?vtuber=星瞳&recent=90")
            
            assert response.status_code == 500
            data = response.json()
            assert data["detail"]["code"] == 500


class TestCurrentStatAPI:
    """Test cases for /basic/stat/current endpoint"""
    
    def test_current_stat_success(self):
        """Test successful current statistics retrieval"""
        mock_stat_record = {
            'uid': '401315430',
            'timestamp': 1747710000,
            'datetime': '2025-05-20T11:00:00',
            'follower_num': 974497,
            'dahanghai_num': 269,
            'video_total_num': 99663571,
            'article_total_num': 13455,
            'likes_total_num': 11801146,
            'elec_num': 1460
        }
        
        with patch('backend.app.get_user_mid', return_value='401315430'), \
             patch('backend.app.query_current_stat_by_mid', new_callable=AsyncMock, return_value=mock_stat_record):
            
            response = client.get("/basic/stat/current?vtuber=星瞳")
            
            assert response.status_code == 200
            data = response.json()
            
            assert data["code"] == 200
            assert data["message"] == "Current statistics retrieved successfully"
            
            response_data = data["data"]
            assert response_data["vtuber_name"] == "星瞳"
            assert response_data["mid"] == "401315430"
            assert "statistics" in response_data
            assert response_data["statistics"]["follower_num"] == 974497
    
    def test_current_stat_no_data(self):
        """Test current statistics when no data found"""
        with patch('backend.app.get_user_mid', return_value='401315430'), \
             patch('backend.app.query_current_stat_by_mid', new_callable=AsyncMock, return_value=None):
            
            response = client.get("/basic/stat/current?vtuber=星瞳")
            
            assert response.status_code == 404
            data = response.json()
            assert data["detail"]["code"] == 404
            assert "No statistics found" in data["detail"]["message"]


class TestHistoricalStatAPI:
    """Test cases for /basic/stat/all endpoint"""
    
    def test_historical_stat_success(self):
        """Test successful historical statistics retrieval"""
        mock_raw_stats = [
            ["2025051415", 99391051, 13427, 11765417, 1456, 972639, 276],
            ["2025051502", 99391051, 13427, 11767636, 1456, 972566, 276]
        ]
        
        with patch('backend.app.get_user_mid', return_value='401315430'), \
             patch('backend.app.query_whole_user_all_stat_by_uid_and_recent', new_callable=AsyncMock, return_value=mock_raw_stats):
            
            response = client.get("/basic/stat/all?vtuber=星瞳&recent=30")
            
            assert response.status_code == 200
            data = response.json()
            
            assert data["code"] == 200
            assert data["message"] == "Historical statistics retrieved successfully"
            
            response_data = data["data"]
            assert response_data["vtuber_name"] == "星瞳"
            assert response_data["period_days"] == 30
            assert response_data["total_records"] == 2
            assert len(response_data["statistics"]) == 2
            
            # Check structured format
            first_stat = response_data["statistics"][0]
            assert first_stat["datetime"] == "2025051415"
            assert first_stat["video_total_num"] == 99391051
            assert first_stat["follower_num"] == 972639
    
    def test_historical_stat_all_data(self):
        """Test historical statistics with recent=-1 (all data)"""
        with patch('backend.app.get_user_mid', return_value='401315430'), \
             patch('backend.app.query_whole_user_all_stat_by_uid_and_recent', new_callable=AsyncMock, return_value=[]):
            
            response = client.get("/basic/stat/all?vtuber=星瞳&recent=-1")
            
            assert response.status_code == 200
            data = response.json()
            
            response_data = data["data"]
            assert response_data["period_days"] == -1
            assert response_data["total_records"] == 0


class TestMedalRankAPI:
    """Test cases for /basic/medal_rank endpoint"""
    
    def test_medal_rank_success(self):
        """Test successful medal ranking retrieval"""
        mock_ranking_data = {
            "uid": "401315430",
            "name": "星瞳",
            "liveid": "22886883",
            "datetime": "2025-05-14T00:00:00",
            "rank_list": [
                {
                    "uid": 486553605,
                    "rank": 1,
                    "level": 34,
                    "uname": "钉钩鱼骑不动",
                    "medal_name": "瞳星结",
                    "guard_level": 2
                }
            ]
        }
        
        with patch('backend.app.get_user_mid', return_value='401315430'), \
             patch('backend.app.query_latest_fans_medal_rank', new_callable=AsyncMock, return_value=mock_ranking_data):
            
            response = client.get("/basic/medal_rank?vtuber=星瞳")
            
            assert response.status_code == 200
            data = response.json()
            
            assert data["code"] == 200
            assert data["message"] == "Fans medal ranking retrieved successfully"
            
            response_data = data["data"]
            assert response_data["vtuber_name"] == "星瞳"
            assert response_data["mid"] == "401315430"
            assert "ranking" in response_data
            assert len(response_data["ranking"]["rank_list"]) == 1
    
    def test_medal_rank_no_data(self):
        """Test medal ranking when no data found"""
        with patch('backend.app.get_user_mid', return_value='401315430'), \
             patch('backend.app.query_latest_fans_medal_rank', new_callable=AsyncMock, return_value=None):
            
            response = client.get("/basic/medal_rank?vtuber=星瞳")
            
            assert response.status_code == 404
            data = response.json()
            assert data["detail"]["code"] == 404
            assert "No fans medal ranking found" in data["detail"]["message"]


class TestResponseFormatConsistency:
    """Test response format consistency across all standardized endpoints"""
    
    def test_success_response_format(self):
        """Test that all success responses follow the same format"""
        endpoints_to_test = [
            ("/basic/dahanghai/rate", {"vtuber": "星瞳", "recent": "90"}),
            ("/basic/follower/rate", {"vtuber": "星瞳", "recent": "90"}),
            ("/basic/stat/current", {"vtuber": "星瞳"}),
            ("/basic/stat/all", {"vtuber": "星瞳", "recent": "30"}),
            ("/basic/medal_rank", {"vtuber": "星瞳"})
        ]
        
        # Mock all dependencies to return successful responses
        with patch('backend.app.get_user_mid', return_value='401315430'), \
             patch('backend.app.calculate_dahanghai_rate_by_mid', new_callable=AsyncMock, return_value='10%'), \
             patch('backend.app.calculate_follower_rate_by_mid', new_callable=AsyncMock, return_value='5%'), \
             patch('backend.app.query_current_stat_by_mid', new_callable=AsyncMock, return_value={'uid': '401315430'}), \
             patch('backend.app.query_whole_user_all_stat_by_uid_and_recent', new_callable=AsyncMock, return_value=[]), \
             patch('backend.app.query_latest_fans_medal_rank', new_callable=AsyncMock, return_value={'uid': '401315430', 'rank_list': []}):
            
            for endpoint, params in endpoints_to_test:
                response = client.get(endpoint, params=params)
                
                assert response.status_code == 200, f"Failed for endpoint {endpoint}"
                data = response.json()
                
                # Check standard response format
                assert "code" in data, f"Missing 'code' in {endpoint}"
                assert "message" in data, f"Missing 'message' in {endpoint}"
                assert "data" in data, f"Missing 'data' in {endpoint}"
                
                assert data["code"] == 200, f"Wrong code in {endpoint}"
                assert isinstance(data["message"], str), f"Message not string in {endpoint}"
                assert isinstance(data["data"], dict), f"Data not dict in {endpoint}"
                
                # Check data structure
                response_data = data["data"]
                assert "vtuber_name" in response_data, f"Missing 'vtuber_name' in {endpoint}"
                assert "mid" in response_data, f"Missing 'mid' in {endpoint}"
    
    def test_error_response_format(self):
        """Test that all error responses follow the same format"""
        # Test 400 errors
        response = client.get("/basic/dahanghai/rate?vtuber=&recent=90")
        assert response.status_code == 400
        data = response.json()
        assert "detail" in data
        assert "code" in data["detail"]
        assert "message" in data["detail"]
        assert data["detail"]["code"] == 400
        
        # Test 404 errors
        with patch('backend.app.get_user_mid', side_effect=Exception("Not found")):
            response = client.get("/basic/dahanghai/rate?vtuber=不存在&recent=90")
            assert response.status_code == 404
            data = response.json()
            assert data["detail"]["code"] == 404


class TestMedalRankTargetAPI:
    """Test cases for /basic/medal_rank/target endpoint"""

    def test_medal_rank_target_success(self):
        """Test successful medal ranking retrieval for target date"""
        mock_ranking_data = {
            "uid": "401315430",
            "name": "星瞳",
            "liveid": "22886883",
            "datetime": "2025-05-14T00:00:00",
            "rank_list": [{"uid": 486553605, "rank": 1, "level": 34}]
        }

        with patch('backend.app.get_user_mid', return_value='401315430'), \
             patch('backend.app.query_fans_medal_rank_by_datetime', new_callable=AsyncMock, return_value=mock_ranking_data):

            response = client.get("/basic/medal_rank/target?target_datetime=2025-05-14&vtuber=星瞳")

            assert response.status_code == 200
            data = response.json()

            assert data["code"] == 200
            assert data["message"] == "Fans medal ranking for target date retrieved successfully"

            response_data = data["data"]
            assert response_data["vtuber_name"] == "星瞳"
            assert response_data["target_date"] == "2025-05-14"
            assert "ranking" in response_data

    def test_medal_rank_target_invalid_date(self):
        """Test medal ranking with invalid date format"""
        response = client.get("/basic/medal_rank/target?target_datetime=invalid-date&vtuber=星瞳")

        assert response.status_code == 400
        data = response.json()
        assert data["detail"]["code"] == 400
        assert "Invalid date format" in data["detail"]["message"]


class TestBasicInfoAPI:
    """Test cases for /basic/info endpoint"""

    def test_basic_info_success(self):
        """Test successful basic info retrieval"""
        mock_user_info = {
            'uid': '401315430',
            'name': '星瞳_Official',
            'face': 'https://i0.hdslb.com/bfs/face/35ce8ca063124d4dda5c211039d0eb67eae3c797.jpg',
            'sign': '时尚虚拟偶像',
            'birthday': '10-21',
            'top_photo': 'http://i1.hdslb.com/bfs/space/cb1c3ef50e22b6096fde67febe863494caefebad.png',
            'room_id': '22886883',
            'live_url': 'https://live.bilibili.com/22886883'
        }

        with patch('backend.app.get_user_mid', return_value='401315430'), \
             patch('backend.app.query_user_info_by_mid', new_callable=AsyncMock, return_value=mock_user_info):

            response = client.get("/basic/info?vtuber=星瞳")

            assert response.status_code == 200
            data = response.json()

            assert data["code"] == 200
            assert data["message"] == "VTuber basic information retrieved successfully"

            response_data = data["data"]
            assert response_data["vtuber_name"] == "星瞳"
            assert "profile" in response_data
            assert response_data["profile"]["name"] == "星瞳_Official"


class TestLiveInfoAPI:
    """Test cases for /live/info endpoint"""

    def test_live_info_success(self):
        """Test successful live info retrieval"""
        mock_live_info = {
            'live_status': 1,
            'live_title': '【3D】下一个更乖',
            'live_cover': 'https://i0.hdslb.com/bfs/live/new_room_cover/8ab848423b88f25c7df6f02a72e1296e1d839032.jpg',
            'parent_area': '虚拟主播',
            'area': '虚拟日常',
            'timestamp': 1747113480,
            'datetime': '2025-05-13T13:18:00',
            'live_action': '开始直播'
        }

        with patch('backend.app.get_user_room_id', return_value='22886883'), \
             patch('backend.app.query_now_live_info_by_room', new_callable=AsyncMock, return_value=mock_live_info):

            response = client.get("/live/info?vtuber=星瞳")

            assert response.status_code == 200
            data = response.json()

            assert data["code"] == 200
            assert data["message"] == "Live streaming information retrieved successfully"

            response_data = data["data"]
            assert response_data["vtuber_name"] == "星瞳"
            assert response_data["room_id"] == "22886883"
            assert "live_info" in response_data
            assert "status_description" in response_data


class TestDynamicsListAPI:
    """Test cases for /dynamics/list endpoint"""

    def test_dynamics_list_success(self):
        """Test successful dynamics list retrieval"""
        mock_dynamics_raw = [
            ["星瞳_Official", "2025-05-14 17:36:07", "小星星们，下周的直播日程表有一点变动",
             "https://www.bilibili.com/opus/1066770950389760019", None, "1066770950389760019",
             1, 158, 1197, "350767500", 11, 4.25]
        ]

        with patch('backend.app.get_user_mid', return_value='401315430'), \
             patch('backend.app.query_user_dynamics_by_mid', new_callable=AsyncMock, return_value=mock_dynamics_raw):

            response = client.get("/dynamics/list?vtuber=星瞳")

            assert response.status_code == 200
            data = response.json()

            assert data["code"] == 200
            assert data["message"] == "Dynamics list retrieved successfully"

            response_data = data["data"]
            assert response_data["vtuber_name"] == "星瞳"
            assert response_data["total_count"] == 1
            assert len(response_data["dynamics"]) == 1

            dynamic = response_data["dynamics"][0]
            assert dynamic["name"] == "星瞳_Official"
            assert dynamic["dynamic_id"] == "1066770950389760019"


class TestCurrentDynamicsAPI:
    """Test cases for /dynamics/current endpoint"""

    def test_current_dynamics_success(self):
        """Test successful current dynamic retrieval"""
        mock_current_dynamic = [
            "星瞳_Official", "2025-05-14 17:36:07", "小星星们，下周的直播日程表有一点变动",
            "https://www.bilibili.com/opus/1066770950389760019", None, "1066770950389760019",
            1, 158, 1197, "350767500", 11
        ]

        with patch('backend.app.get_user_mid', return_value='401315430'), \
             patch('backend.app.query_current_dynamics', new_callable=AsyncMock, return_value=mock_current_dynamic):

            response = client.get("/dynamics/current?vtuber=星瞳")

            assert response.status_code == 200
            data = response.json()

            assert data["code"] == 200
            assert data["message"] == "Current dynamic retrieved successfully"

            response_data = data["data"]
            assert response_data["vtuber_name"] == "星瞳"
            assert "current_dynamic" in response_data
            assert response_data["current_dynamic"]["name"] == "星瞳_Official"

    def test_current_dynamics_no_data(self):
        """Test current dynamic when no data found"""
        with patch('backend.app.get_user_mid', return_value='401315430'), \
             patch('backend.app.query_current_dynamics', new_callable=AsyncMock, return_value=[None] * 11):

            response = client.get("/dynamics/current?vtuber=星瞳")

            assert response.status_code == 404
            data = response.json()
            assert data["detail"]["code"] == 404


class TestVideoListAPI:
    """Test cases for /video/list endpoint"""

    def test_video_list_success(self):
        """Test successful video list retrieval"""
        mock_videos_raw = [
            ["星瞳_Official", "BV1iSVqzDE2b", 1747113480, "2025-05-05 18:00:00", "一秒回到放假前！！！！",
             "表演/出镜：星瞳", "http://i1.hdslb.com/bfs/archive/1e8938df5b9b92f9d38b40c380bca63f1eaa69a9.jpg",
             37809, 196, 3710, 1084, 494, 88, 29, "114453983005561", "00:07", "", 0, "{}", "", 5.24]
        ]

        with patch('backend.app.get_user_mid', return_value='401315430'), \
             patch('backend.app.query_all_video_list_by_mid', new_callable=AsyncMock, return_value=mock_videos_raw):

            response = client.get("/video/list?vtuber=星瞳")

            assert response.status_code == 200
            data = response.json()

            assert data["code"] == 200
            assert data["message"] == "Video list retrieved successfully"

            response_data = data["data"]
            assert response_data["vtuber_name"] == "星瞳"
            assert response_data["total_count"] == 1
            assert len(response_data["videos"]) == 1

            video = response_data["videos"][0]
            assert video["bvid"] == "BV1iSVqzDE2b"
            assert video["title"] == "一秒回到放假前！！！！"


class TestVideoTopNAPI:
    """Test cases for /video/top_n/ endpoint"""

    def test_video_top_n_success(self):
        """Test successful top N videos retrieval"""
        mock_top_videos = [
            {
                "bvid": "BV1ocfaYBEo8",
                "name": "新中式舞台《寄明月》",
                "face": "http://i1.hdslb.com/bfs/archive/ffb163d328350bc22f9f3faebce28aa8fdc04324.jpg",
                "heat": 8.15,
                "pubtime": "2025-01-22",
                "honor_short": "全站排行榜最高第37名,热门收录",
                "honor_count": 2
            }
        ]

        with patch('backend.app.get_user_mid', return_value='401315430'), \
             patch('backend.app.query_top_n_videos', new_callable=AsyncMock, return_value=mock_top_videos):

            response = client.get("/video/top_n/?vtuber=星瞳&s=2023-01-01&e=2024-12-31&n=10")

            assert response.status_code == 200
            data = response.json()

            assert data["code"] == 200
            assert data["message"] == "Top N videos retrieved successfully"

            response_data = data["data"]
            assert response_data["vtuber_name"] == "星瞳"
            assert response_data["query_params"]["start_date"] == "2023-01-01"
            assert response_data["query_params"]["end_date"] == "2024-12-31"
            assert response_data["query_params"]["limit"] == 10
            assert response_data["total_count"] == 1

    def test_video_top_n_invalid_params(self):
        """Test top N videos with invalid parameters"""
        # Test invalid n parameter
        response = client.get("/video/top_n/?vtuber=星瞳&s=2023-01-01&e=2024-12-31&n=0")
        assert response.status_code == 400

        # Test invalid date format
        response = client.get("/video/top_n/?vtuber=星瞳&s=invalid-date&e=2024-12-31&n=10")
        assert response.status_code == 400


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
