"""
RESTful API 测试运行脚本
提供多种测试选项
"""

import subprocess
import sys
import os

def run_design_test():
    """运行设计测试（不依赖数据库）"""
    print("🚀 运行 RESTful API 设计测试...")
    try:
        result = subprocess.run([sys.executable, "tests/test_design_only.py"], 
                              capture_output=True, text=True, cwd=os.getcwd())
        print(result.stdout)
        if result.stderr:
            print("错误输出:", result.stderr)
        return result.returncode == 0
    except Exception as e:
        print(f"运行设计测试失败: {e}")
        return False

def run_mock_test():
    """运行模拟测试"""
    print("🚀 运行模拟数据库测试...")
    try:
        result = subprocess.run([sys.executable, "-m", "pytest", "tests/test_api_mock.py", "-v"], 
                              capture_output=True, text=True, cwd=os.getcwd())
        print(result.stdout)
        if result.stderr:
            print("错误输出:", result.stderr)
        return result.returncode == 0
    except Exception as e:
        print(f"运行模拟测试失败: {e}")
        return False

def run_full_test():
    """运行完整测试（需要数据库）"""
    print("🚀 运行完整 API 测试...")
    try:
        result = subprocess.run([sys.executable, "-m", "pytest", "tests/test_api_restful.py", "-v"], 
                              capture_output=True, text=True, cwd=os.getcwd())
        print(result.stdout)
        if result.stderr:
            print("错误输出:", result.stderr)
        return result.returncode == 0
    except Exception as e:
        print(f"运行完整测试失败: {e}")
        return False

def run_manual_test():
    """运行手动测试"""
    print("🚀 运行手动 API 测试...")
    print("注意：此测试需要后端服务器在 http://localhost:9022 运行")
    try:
        result = subprocess.run([sys.executable, "tests/manual_api_test.py"], 
                              capture_output=True, text=True, cwd=os.getcwd())
        print(result.stdout)
        if result.stderr:
            print("错误输出:", result.stderr)
        return result.returncode == 0
    except Exception as e:
        print(f"运行手动测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("RESTful API 测试运行器")
    print("=" * 60)
    
    print("\n请选择要运行的测试类型：")
    print("1. 设计测试（推荐，不需要数据库）")
    print("2. 模拟测试（使用模拟数据库）")
    print("3. 完整测试（需要数据库连接）")
    print("4. 手动测试（需要启动后端服务器）")
    print("5. 运行所有测试")
    print("0. 退出")
    
    while True:
        try:
            choice = input("\n请输入选择 (0-5): ").strip()
            
            if choice == "0":
                print("退出测试运行器")
                break
            elif choice == "1":
                success = run_design_test()
                print(f"\n设计测试 {'✅ 成功' if success else '❌ 失败'}")
            elif choice == "2":
                success = run_mock_test()
                print(f"\n模拟测试 {'✅ 成功' if success else '❌ 失败'}")
            elif choice == "3":
                success = run_full_test()
                print(f"\n完整测试 {'✅ 成功' if success else '❌ 失败'}")
            elif choice == "4":
                success = run_manual_test()
                print(f"\n手动测试 {'✅ 成功' if success else '❌ 失败'}")
            elif choice == "5":
                print("\n运行所有测试...")
                results = []
                results.append(("设计测试", run_design_test()))
                results.append(("模拟测试", run_mock_test()))
                results.append(("完整测试", run_full_test()))
                results.append(("手动测试", run_manual_test()))
                
                print("\n" + "=" * 40)
                print("测试结果总结:")
                for test_name, success in results:
                    print(f"{test_name}: {'✅ 成功' if success else '❌ 失败'}")
                print("=" * 40)
            else:
                print("无效选择，请输入 0-5")
                
        except KeyboardInterrupt:
            print("\n\n用户中断，退出测试运行器")
            break
        except Exception as e:
            print(f"发生错误: {e}")

if __name__ == "__main__":
    main()
