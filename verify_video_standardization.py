"""
Verification script for video-related API standardization.
Checks if all video APIs follow the new RESTful standards.
"""

import ast
import re
from pathlib import Path

def check_function_standardization(func_code, func_name):
    """Check if a function follows standardization patterns"""
    checks = {
        'has_try_catch': False,
        'has_parameter_validation': False,
        'has_create_success_response': False,
        'has_create_error_response': False,
        'has_http_exception': False,
        'has_standard_docstring': False,
        'has_timestamp': False
    }
    
    # Check for try-catch blocks
    if 'try:' in func_code and 'except' in func_code:
        checks['has_try_catch'] = True
    
    # Check for parameter validation
    validation_patterns = [
        r'if .+ <= 0:',
        r'if not .+ or not .+\.strip\(\):',
        r'raise HTTPException.*400',
        r'Parameter .+ cannot be empty'
    ]
    for pattern in validation_patterns:
        if re.search(pattern, func_code):
            checks['has_parameter_validation'] = True
            break
    
    # Check for success response creation
    if 'create_success_response' in func_code:
        checks['has_create_success_response'] = True
    
    # Check for error response creation
    if 'create_error_response' in func_code:
        checks['has_create_error_response'] = True
    
    # Check for HTTP exceptions
    if 'HTTPException' in func_code:
        checks['has_http_exception'] = True
    
    # Check for standard English docstring
    docstring_match = re.search(r'"""(.*?)"""', func_code, re.DOTALL)
    if docstring_match:
        docstring = docstring_match.group(1)
        if any(keyword in docstring for keyword in ['Args:', 'Returns:', 'Example:']):
            checks['has_standard_docstring'] = True
    
    # Check for timestamp
    if 'datetime.now().isoformat()' in func_code or 'retrieved_at' in func_code:
        checks['has_timestamp'] = True
    
    return checks

def verify_video_standardization():
    """Verify standardization of video-related API functions"""
    
    # List of video-related functions to check
    video_functions = [
        'read_recent_video_day_views',
        'read_target_video_day_views',
        'read_top_n_video_day_by_time',
        'read_top_n_video_day_by_period',
        'read_target_dahanghai_whole_list',
        'read_recent_info'
    ]
    
    app_file = Path('backend/app.py')
    if not app_file.exists():
        print("❌ backend/app.py file not found!")
        return False
    
    with open(app_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("🔍 Video API Standardization Verification")
    print("=" * 60)
    
    all_passed = True
    total_functions = len(video_functions)
    passed_functions = 0
    
    for func_name in video_functions:
        print(f"\n📹 Checking {func_name}...")
        
        # Extract function code
        pattern = rf'async def {func_name}\(.*?\n(?:(?!async def|@app\.|class ).)*'
        match = re.search(pattern, content, re.DOTALL)
        
        if not match:
            print(f"   ❌ Function {func_name} not found")
            all_passed = False
            continue
        
        func_code = match.group(0)
        checks = check_function_standardization(func_code, func_name)
        
        # Display results
        function_passed = True
        for check_name, passed in checks.items():
            status = "✅" if passed else "❌"
            check_display = check_name.replace('_', ' ').title()
            print(f"   {status} {check_display}")
            if not passed:
                function_passed = False
                all_passed = False
        
        if function_passed:
            passed_functions += 1
            print(f"   ✅ {func_name} - All checks passed!")
        else:
            print(f"   ❌ {func_name} - Some checks failed")
    
    print("\n" + "=" * 60)
    print(f"📊 Video API Standardization Summary:")
    print(f"   • Functions checked: {total_functions}")
    print(f"   • Functions passed: {passed_functions}")
    print(f"   • Success rate: {passed_functions/total_functions*100:.1f}%")
    
    if all_passed:
        print("\n🎉 All video API functions are properly standardized!")
        print("\n✨ Standardization Features Verified:")
        print("   • ✅ Unified response format implemented")
        print("   • ✅ Parameter validation added")
        print("   • ✅ Error handling standardized")
        print("   • ✅ HTTP status codes properly used")
        print("   • ✅ Documentation updated")
        print("   • ✅ Timestamp formatting added")
        print("   • ✅ Try-catch blocks implemented")
    else:
        print(f"\n⚠️  {total_functions - passed_functions} functions need attention")
        print("\n🔧 Common issues to fix:")
        print("   • Add try-catch blocks for error handling")
        print("   • Implement parameter validation")
        print("   • Use create_success_response() and create_error_response()")
        print("   • Add proper HTTP exception handling")
        print("   • Update docstrings to English with Args/Returns/Example")
        print("   • Include timestamp information in responses")
    
    return all_passed

def check_response_format_consistency():
    """Check if all functions use consistent response format"""
    print("\n🔍 Checking Video API Response Format Consistency")
    print("-" * 50)
    
    app_file = Path('backend/app.py')
    with open(app_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check for consistent response structure patterns in video functions
    video_section = content[content.find('read_recent_video_day_views'):content.find('read_recent_info') + 2000]
    
    response_patterns = {
        'Success Response': r'create_success_response\(',
        'BVID Field': r'"bvid":\s*bvid',
        'VTuber Name': r'"vtuber_name":\s*vtuber',
        'MID Field': r'"mid":\s*mid',
        'Timestamp': r'"retrieved_at":\s*datetime\.now\(\)\.isoformat\(\)',
        'Query Params': r'"query_params":\s*{',
        'Error Response': r'create_error_response\(',
        'HTTP Exception': r'HTTPException\(',
        'Parameter Validation': r'if .+ <= 0:'
    }
    
    consistency_results = {}
    for pattern_name, pattern in response_patterns.items():
        matches = len(re.findall(pattern, video_section))
        consistency_results[pattern_name] = matches
        expected_min = 4 if pattern_name in ['Success Response', 'Error Response', 'HTTP Exception'] else 2
        status = "✅" if matches >= expected_min else "⚠️"
        print(f"   {status} {pattern_name}: {matches} occurrences")
    
    consistent_patterns = sum(1 for count in consistency_results.values() if count >= 2)
    total_patterns = len(response_patterns)
    
    print(f"\n📊 Video Response Format Consistency: {consistent_patterns}/{total_patterns} patterns consistent")
    return consistent_patterns >= total_patterns * 0.8  # 80% threshold

def main():
    """Main verification function"""
    print("🚀 Video API Standardization Verification")
    print("=" * 60)
    print("Verifying 6 video-related API endpoints...")
    print()
    
    # Verify standardization
    standardization_passed = verify_video_standardization()
    
    # Check response format consistency
    consistency_passed = check_response_format_consistency()
    
    print("\n" + "=" * 60)
    print("🏁 Final Video Verification Results:")
    print(f"   • Video API Standardization: {'✅ PASSED' if standardization_passed else '❌ FAILED'}")
    print(f"   • Response Consistency: {'✅ PASSED' if consistency_passed else '❌ FAILED'}")
    
    if standardization_passed and consistency_passed:
        print("\n🎉 All video APIs are successfully standardized!")
        print("   📹 Video view tracking endpoints standardized")
        print("   📊 Top video ranking endpoints standardized")
        print("   🛡️ Dahanghai list endpoints standardized")
        print("   📋 Recent activity endpoints standardized")
    else:
        print("\n⚠️  Some issues found. Please review and fix before deployment.")
    
    return standardization_passed and consistency_passed

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
