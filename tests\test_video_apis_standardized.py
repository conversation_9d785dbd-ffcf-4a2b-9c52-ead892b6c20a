"""
Test suite for standardized video-related API endpoints.
Tests the new RESTful API interfaces for video functionality.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pytest
from unittest.mock import patch, AsyncMock
from fastapi.testclient import TestClient
from backend.app import app

client = TestClient(app)

class TestVideoAPIsStandardized:
    """Test class for standardized video API endpoints"""
    
    def setup_method(self):
        """Setup test data"""
        self.test_vtuber = "星瞳"
        self.test_mid = "401315430"
        self.test_room_id = "22625025"
        self.test_bvid = "BV1a3DqYZErW"
        self.test_date = "2024-11-22"
        
        # Mock data for video view records
        self.mock_video_records = [
            {
                "id": 63391,
                "uid": "401315430",
                "name": "星瞳",
                "bvid": "BV1a3DqYZErW",
                "title": "40秒梦幻旅行，跟我一起遨游天际！【iykyk】",
                "create_time": 1731146400,
                "datetime": "2024-11-22T00:00:00",
                "view_num": 100740,
                "view_rise_num": 4
            }
        ]
        
        # Mock data for dahanghai list
        self.mock_dahanghai_list = [
            {
                "up_uid": "401315430",
                "up_name": "星瞳",
                "time": "2025-05-14",
                "datetime": "2025-05-14T15:19:53.561790",
                "num": 276,
                "page": 1,
                "uid": 2168222,
                "ruid": 401315430,
                "rank": 1,
                "username": "bili_2976",
                "face": "https://i0.hdslb.com/bfs/face/member/noface.jpg",
                "guard_level": 1,
                "guard_sub_level": 0,
                "if_top3": True
            }
        ]

    @patch('backend.app.query_single_video_rencent_day_data')
    def test_read_recent_video_day_views_success(self, mock_query):
        """Test successful recent video view data retrieval"""
        mock_query.return_value = self.mock_video_records
        
        response = client.get(f"/video/views/recent?bvid={self.test_bvid}&recent=20")
        
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        assert data["message"] == "Recent video view data retrieved successfully"
        assert data["data"]["bvid"] == self.test_bvid
        assert data["data"]["query_params"]["recent_days"] == 20
        assert len(data["data"]["view_data"]) == 1
        assert data["data"]["total_records"] == 1

    @patch('backend.app.query_single_video_target_day_data')
    def test_read_target_video_day_views_success(self, mock_query):
        """Test successful target date video view data retrieval"""
        mock_query.return_value = self.mock_video_records[0]
        
        response = client.get(f"/video/views/target?bvid={self.test_bvid}&time={self.test_date}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        assert data["message"] == "Target date video view data retrieved successfully"
        assert data["data"]["bvid"] == self.test_bvid
        assert data["data"]["target_date"] == self.test_date
        assert data["data"]["has_data"] == True
        assert data["data"]["view_data"]["title"] == "40秒梦幻旅行，跟我一起遨游天际！【iykyk】"

    @patch('backend.app.get_user_mid')
    @patch('backend.app.query_top_n_view_rise_target_day_data')
    def test_read_top_n_video_day_by_time_success(self, mock_query, mock_get_mid):
        """Test successful top N videos by time retrieval"""
        mock_get_mid.return_value = self.test_mid
        mock_query.return_value = self.mock_video_records
        
        response = client.get(f"/video/views/top_n/day?time={self.test_date}&n=20&vtuber={self.test_vtuber}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        assert data["message"] == "Top N videos by view increase retrieved successfully"
        assert data["data"]["vtuber_name"] == self.test_vtuber
        assert data["data"]["mid"] == self.test_mid
        assert data["data"]["query_params"]["target_date"] == self.test_date
        assert data["data"]["query_params"]["limit"] == 20
        assert len(data["data"]["top_videos"]) == 1

    @patch('backend.app.get_user_mid')
    @patch('backend.app.query_top_n_view_rise_day_data_period')
    def test_read_top_n_video_day_by_period_success(self, mock_query, mock_get_mid):
        """Test successful top N videos by period retrieval"""
        mock_get_mid.return_value = self.test_mid
        mock_query.return_value = self.mock_video_records
        
        start_date = "2024-11-21"
        end_date = "2024-11-22"
        
        response = client.get(f"/video/views/top_n/period?s={start_date}&e={end_date}&n=20&vtuber={self.test_vtuber}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        assert data["message"] == "Top N videos by view increase in period retrieved successfully"
        assert data["data"]["query_params"]["start_date"] == start_date
        assert data["data"]["query_params"]["end_date"] == end_date
        assert data["data"]["query_params"]["period_days"] == 1

    @patch('backend.app.get_user_mid')
    @patch('backend.app.query_dahanghai_list_by_uid_and_datetime')
    def test_read_target_dahanghai_whole_list_success(self, mock_query, mock_get_mid):
        """Test successful dahanghai list retrieval"""
        mock_get_mid.return_value = self.test_mid
        mock_query.return_value = self.mock_dahanghai_list
        
        target_date = "2025-05-14"
        response = client.get(f"/basic/dahanghai/list/target?target_datetime={target_date}&vtuber={self.test_vtuber}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        assert data["message"] == "Dahanghai list retrieved successfully"
        assert data["data"]["vtuber_name"] == self.test_vtuber
        assert data["data"]["target_date"] == target_date
        assert data["data"]["has_data"] == True
        assert len(data["data"]["dahanghai_list"]) == 1

    @patch('backend.app.get_user_mid')
    @patch('backend.app.get_user_room_id')
    @patch('backend.app.query_recent_info')
    def test_read_recent_info_success(self, mock_query, mock_get_room_id, mock_get_mid):
        """Test successful recent info retrieval"""
        from backend.base.dataclasses import VupRencentInfo
        
        mock_get_mid.return_value = self.test_mid
        mock_get_room_id.return_value = self.test_room_id
        
        mock_recent_info = VupRencentInfo(
            time="2025-05-28",
            name="星瞳_Official",
            follower_change=-44,
            dahanghai_change=-1,
            video_content=["2025-05-27 18:32:29", "打CALL教程❌体能测试✅", "14100", ""],
            dynamic_content=["2025-05-27 18:32:29", "叮铃铃~在充满爱的起床铃中举起了双手~"],
            live_content="暂无直播信息",
            relations=["星瞳邀请了@东爱璃Lovely 和 @雪糕cheese 一起直播游玩《胜利女神：新的希望》。"],
            rise_videos=[["BV1c6jdzHE6x", "N/A", "打CALL教程❌体能测试✅", 15472, 3572]],
            tieba_topic=[{"topic": "新的一天，新的加9？", "rank": 1, "heat": 5}]
        )
        mock_query.return_value = mock_recent_info
        
        response = client.get(f"/info/recent/dict?vtuber={self.test_vtuber}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        assert data["message"] == "Recent activity information retrieved successfully"
        assert data["data"]["vtuber_name"] == self.test_vtuber
        assert data["data"]["mid"] == self.test_mid
        assert data["data"]["room_id"] == self.test_room_id
        assert data["data"]["recent_info"]["name"] == "星瞳_Official"
        assert data["data"]["recent_info"]["follower_change"] == -44

    def test_parameter_validation_errors(self):
        """Test parameter validation for various endpoints"""
        # Test empty BVID
        response = client.get("/video/views/recent?bvid=&recent=20")
        assert response.status_code == 400
        
        # Test invalid BVID format
        response = client.get("/video/views/recent?bvid=invalid&recent=20")
        assert response.status_code == 400
        
        # Test negative recent parameter
        response = client.get(f"/video/views/recent?bvid={self.test_bvid}&recent=-1")
        assert response.status_code == 400
        
        # Test excessive recent parameter
        response = client.get(f"/video/views/recent?bvid={self.test_bvid}&recent=400")
        assert response.status_code == 400
        
        # Test invalid date format
        response = client.get(f"/video/views/target?bvid={self.test_bvid}&time=invalid-date")
        assert response.status_code == 400
        
        # Test excessive n parameter
        response = client.get(f"/video/views/top_n/day?time={self.test_date}&n=200&vtuber={self.test_vtuber}")
        assert response.status_code == 400

    @patch('backend.app.get_user_mid')
    def test_vtuber_not_found_error(self, mock_get_mid):
        """Test VTuber not found error handling"""
        mock_get_mid.side_effect = Exception("VTuber not found")
        
        response = client.get(f"/video/views/top_n/day?time={self.test_date}&n=20&vtuber=不存在的VTuber")
        assert response.status_code == 404

    @patch('backend.app.query_single_video_rencent_day_data')
    def test_no_data_found_error(self, mock_query):
        """Test no data found error handling"""
        mock_query.return_value = None
        
        response = client.get(f"/video/views/recent?bvid={self.test_bvid}&recent=20")
        assert response.status_code == 404

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
