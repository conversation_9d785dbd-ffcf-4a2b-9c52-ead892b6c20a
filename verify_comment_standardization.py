"""
Verification script for comment-related API standardization.
Checks if all comment APIs follow the new RESTful standards.
"""

import ast
import re
from pathlib import Path

def check_function_standardization(func_code, func_name):
    """Check if a function follows standardization patterns"""
    checks = {
        'has_try_catch': False,
        'has_parameter_validation': False,
        'has_create_success_response': False,
        'has_create_error_response': False,
        'has_http_exception': False,
        'has_standard_docstring': False,
        'has_timestamp': False
    }
    
    # Check for try-catch blocks
    if 'try:' in func_code and 'except' in func_code:
        checks['has_try_catch'] = True
    
    # Check for parameter validation
    validation_patterns = [
        r'if not .+ or not .+\.strip\(\):',
        r'raise HTTPException.*400',
        r'Parameter .+ cannot be empty'
    ]
    for pattern in validation_patterns:
        if re.search(pattern, func_code):
            checks['has_parameter_validation'] = True
            break
    
    # Check for success response creation
    if 'create_success_response' in func_code:
        checks['has_create_success_response'] = True
    
    # Check for error response creation
    if 'create_error_response' in func_code:
        checks['has_create_error_response'] = True
    
    # Check for HTTP exceptions
    if 'HTTPException' in func_code:
        checks['has_http_exception'] = True
    
    # Check for standard English docstring
    docstring_match = re.search(r'"""(.*?)"""', func_code, re.DOTALL)
    if docstring_match:
        docstring = docstring_match.group(1)
        if any(keyword in docstring for keyword in ['Args:', 'Returns:', 'Example:']):
            checks['has_standard_docstring'] = True
    
    # Check for timestamp
    if 'datetime.now().isoformat()' in func_code or 'retrieved_at' in func_code:
        checks['has_timestamp'] = True
    
    return checks

def verify_comment_standardization():
    """Verify standardization of comment-related API functions"""
    
    # List of comment-related functions to check
    comment_functions = [
        'read_comment_video',
        'read_comment_dynamic', 
        'read_comment_top_n',
        'read_comment_top_n_user',
        'read_comment_wordcloud',
        'read_whole_tieba',
        'read_thread_tieba'
    ]
    
    app_file = Path('backend/app.py')
    if not app_file.exists():
        print("❌ backend/app.py file not found!")
        return False
    
    with open(app_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("🔍 Verifying Comment API Standardization")
    print("=" * 60)
    
    all_passed = True
    total_functions = len(comment_functions)
    passed_functions = 0
    
    for func_name in comment_functions:
        print(f"\n📡 Checking {func_name}...")
        
        # Extract function code
        pattern = rf'async def {func_name}\(.*?\n(?:(?!async def|@app\.|class ).)*'
        match = re.search(pattern, content, re.DOTALL)
        
        if not match:
            print(f"   ❌ Function {func_name} not found")
            all_passed = False
            continue
        
        func_code = match.group(0)
        checks = check_function_standardization(func_code, func_name)
        
        # Display results
        function_passed = True
        for check_name, passed in checks.items():
            status = "✅" if passed else "❌"
            check_display = check_name.replace('_', ' ').title()
            print(f"   {status} {check_display}")
            if not passed:
                function_passed = False
                all_passed = False
        
        if function_passed:
            passed_functions += 1
            print(f"   ✅ {func_name} - All checks passed!")
        else:
            print(f"   ❌ {func_name} - Some checks failed")
    
    print("\n" + "=" * 60)
    print(f"📊 Standardization Summary:")
    print(f"   • Functions checked: {total_functions}")
    print(f"   • Functions passed: {passed_functions}")
    print(f"   • Success rate: {passed_functions/total_functions*100:.1f}%")
    
    if all_passed:
        print("\n🎉 All comment API functions are properly standardized!")
        print("\n✨ Standardization Features Verified:")
        print("   • ✅ Unified response format implemented")
        print("   • ✅ Parameter validation added")
        print("   • ✅ Error handling standardized")
        print("   • ✅ HTTP status codes properly used")
        print("   • ✅ Documentation updated")
        print("   • ✅ Timestamp formatting added")
        print("   • ✅ Try-catch blocks implemented")
    else:
        print(f"\n⚠️  {total_functions - passed_functions} functions need attention")
        print("\n🔧 Common issues to fix:")
        print("   • Add try-catch blocks for error handling")
        print("   • Implement parameter validation")
        print("   • Use create_success_response() and create_error_response()")
        print("   • Add proper HTTP exception handling")
        print("   • Update docstrings to English with Args/Returns/Example")
        print("   • Include timestamp information in responses")
    
    return all_passed

def check_response_format_consistency():
    """Check if all functions use consistent response format"""
    print("\n🔍 Checking Response Format Consistency")
    print("-" * 40)
    
    app_file = Path('backend/app.py')
    with open(app_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check for consistent response structure
    response_patterns = [
        r'create_success_response\(',
        r'"vtuber_name":\s*vtuber',
        r'"mid":\s*mid',
        r'"retrieved_at":\s*datetime\.now\(\)\.isoformat\(\)',
        r'"total_count":\s*len\('
    ]
    
    consistency_score = 0
    for pattern in response_patterns:
        matches = len(re.findall(pattern, content))
        if matches >= 6:  # Should appear in most comment functions
            consistency_score += 1
            print(f"   ✅ Pattern '{pattern}' found {matches} times")
        else:
            print(f"   ⚠️  Pattern '{pattern}' found only {matches} times")
    
    print(f"\n📊 Response Format Consistency: {consistency_score}/{len(response_patterns)} patterns consistent")
    return consistency_score == len(response_patterns)

def main():
    """Main verification function"""
    print("🚀 Comment API Standardization Verification")
    print("=" * 60)
    
    # Verify standardization
    standardization_passed = verify_comment_standardization()
    
    # Check response format consistency
    consistency_passed = check_response_format_consistency()
    
    print("\n" + "=" * 60)
    print("📋 Final Verification Results:")
    print(f"   • Standardization: {'✅ PASSED' if standardization_passed else '❌ FAILED'}")
    print(f"   • Response Consistency: {'✅ PASSED' if consistency_passed else '❌ FAILED'}")
    
    if standardization_passed and consistency_passed:
        print("\n🎉 All comment APIs are successfully standardized!")
        print("   Ready for production use with consistent RESTful interface.")
    else:
        print("\n⚠️  Some issues found. Please review and fix before deployment.")
    
    return standardization_passed and consistency_passed

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
