"""
Test suite for standardized comment-related API endpoints.
Tests the new RESTful API interfaces for comment functionality.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pytest
from unittest.mock import patch, AsyncMock
from fastapi.testclient import TestClient
from backend.app import app

client = TestClient(app)

class TestCommentAPIsStandardized:
    """Test class for standardized comment API endpoints"""
    
    def setup_method(self):
        """Setup test data"""
        self.test_vtuber = "星瞳"
        self.test_mid = "401315430"
        self.test_start_date = "2023-01-01"
        self.test_end_date = "2024-12-31"
        
        # Mock data for video comments
        self.mock_video_comments = [
            ["星瞳", "测试用户", "这是一条测试评论", "2024-05-19 22:24:28", 10, "123456", "789012", 
             "https://example.com/face.jpg", 2, "0", "video123", 12, 0.8]
        ]
        
        # Mock data for dynamic comments
        self.mock_dynamic_comments = [
            ["星瞳", "动态用户", "动态测试评论", "2024-05-19 22:24:28", 5, "654321", "210987", 
             "https://example.com/face2.jpg", 1, "0", "dynamic456", 6, 0.7]
        ]
        
        # Mock data for top comments
        self.mock_top_comments = [
            {
                "name": "热门用户",
                "uid": "111111",
                "face": "https://example.com/face3.jpg",
                "time": "2024-05-19 22:24:28",
                "comment": "这是热门评论",
                "heat": 100,
                "from_id": "BV123456",
                "from_name": "热门视频",
                "source": "video"
            }
        ]
        
        # Mock data for top users
        self.mock_top_users = (
            [{"name": "点赞王", "likeNum": 1000, "uid": "user1", "face": "face1.jpg"}],
            [{"name": "评论王", "appealNum": 500, "uid": "user2", "face": "face2.jpg"}],
            [{"name": "回复王", "rcountSum": 200, "uid": "user3", "face": "face3.jpg"}]
        )
        
        # Mock data for Tieba
        self.mock_tieba_data = [
            ["27776437", "星瞳official", "9015559022", "测试用户", "2024-05-13 23:01:28", 
             "2024-05-13 23:01:28", "测试标题", "测试内容", "test.jpg", 100, 5, 3, 0, 1, "123", 1]
        ]

    @patch('backend.app.get_user_mid')
    @patch('backend.app.query_all_video_comments_by_mid')
    def test_read_comment_video_success(self, mock_query, mock_get_mid):
        """Test successful video comments retrieval"""
        mock_get_mid.return_value = self.test_mid
        mock_query.return_value = self.mock_video_comments
        
        response = client.get(f"/comment/video?vtuber={self.test_vtuber}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        assert data["message"] == "Video comments retrieved successfully"
        assert data["data"]["vtuber_name"] == self.test_vtuber
        assert data["data"]["mid"] == self.test_mid
        assert len(data["data"]["comments"]) == 1
        assert data["data"]["total_count"] == 1
        
        comment = data["data"]["comments"][0]
        assert comment["up_name"] == "星瞳"
        assert comment["commenter_name"] == "测试用户"
        assert comment["comment"] == "这是一条测试评论"

    @patch('backend.app.get_user_mid')
    @patch('backend.app.query_all_dynamics_comments_by_mid')
    def test_read_comment_dynamic_success(self, mock_query, mock_get_mid):
        """Test successful dynamic comments retrieval"""
        mock_get_mid.return_value = self.test_mid
        mock_query.return_value = self.mock_dynamic_comments
        
        response = client.get(f"/comment/dynamic?vtuber={self.test_vtuber}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        assert data["message"] == "Dynamic comments retrieved successfully"
        assert data["data"]["vtuber_name"] == self.test_vtuber
        assert len(data["data"]["comments"]) == 1

    @patch('backend.app.get_user_mid')
    @patch('backend.app.query_top_n_comments')
    def test_read_comment_top_n_success(self, mock_query, mock_get_mid):
        """Test successful top N comments retrieval"""
        mock_get_mid.return_value = self.test_mid
        mock_query.return_value = self.mock_top_comments
        
        response = client.get(
            f"/comment/top_n/?vtuber={self.test_vtuber}&s={self.test_start_date}&e={self.test_end_date}&n=10&source=video"
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        assert data["message"] == "Top N comments by heat retrieved successfully"
        assert data["data"]["query_params"]["start_date"] == self.test_start_date
        assert data["data"]["query_params"]["end_date"] == self.test_end_date
        assert data["data"]["query_params"]["limit"] == 10
        assert len(data["data"]["top_comments"]) == 1

    @patch('backend.app.get_user_mid')
    @patch('backend.app.query_top_n_comments_user')
    def test_read_comment_top_n_user_success(self, mock_query, mock_get_mid):
        """Test successful top N users retrieval"""
        mock_get_mid.return_value = self.test_mid
        mock_query.return_value = self.mock_top_users
        
        response = client.get(
            f"/comment/top_n_user/?vtuber={self.test_vtuber}&s={self.test_start_date}&e={self.test_end_date}&n=10&source=all"
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        assert data["message"] == "Top N commenting users retrieved successfully"
        assert "user_rankings" in data["data"]
        assert "top_likes_users" in data["data"]["user_rankings"]
        assert "top_comments_users" in data["data"]["user_rankings"]
        assert "top_replies_users" in data["data"]["user_rankings"]

    @patch('backend.app.get_user_mid')
    @patch('backend.app.U.get_zh_role_name')
    @patch('backend.app.query_comment_wordcloud')
    def test_read_comment_wordcloud_success(self, mock_query, mock_get_zh, mock_get_mid):
        """Test successful word cloud generation"""
        mock_get_mid.return_value = self.test_mid
        mock_get_zh.return_value = "星瞳"
        mock_query.return_value = "/wordcloud/星瞳.png"
        
        response = client.get(
            f"/comment/wordcloud/?vtuber={self.test_vtuber}&s={self.test_start_date}&e={self.test_end_date}"
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        assert data["message"] == "Word cloud generated successfully"
        assert data["data"]["wordcloud"]["image_path"] == "/wordcloud/星瞳.png"
        assert "image_url" in data["data"]["wordcloud"]

    @patch('backend.app.get_user_mid')
    @patch('backend.app.query_tieba_whole')
    def test_read_whole_tieba_success(self, mock_query, mock_get_mid):
        """Test successful Tieba data retrieval"""
        mock_get_mid.return_value = self.test_mid
        mock_query.return_value = self.mock_tieba_data
        
        response = client.get(
            f"/tieba/whole/?vtuber={self.test_vtuber}&s={self.test_start_date}&e={self.test_end_date}"
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        assert data["message"] == "Tieba data retrieved successfully"
        assert len(data["data"]["tieba_data"]) == 1
        
        tieba_item = data["data"]["tieba_data"][0]
        assert tieba_item["fid"] == "27776437"
        assert tieba_item["fname"] == "星瞳official"
        assert tieba_item["title"] == "测试标题"

    def test_parameter_validation_errors(self):
        """Test parameter validation for various endpoints"""
        # Test empty vtuber parameter
        response = client.get("/comment/video?vtuber=")
        assert response.status_code == 400
        
        # Test invalid date format
        response = client.get("/comment/top_n/?vtuber=星瞳&s=invalid-date&e=2024-12-31&n=10")
        assert response.status_code == 400
        
        # Test negative n parameter
        response = client.get("/comment/top_n/?vtuber=星瞳&s=2023-01-01&e=2024-12-31&n=-1")
        assert response.status_code == 400
        
        # Test invalid source parameter
        response = client.get("/comment/top_n/?vtuber=星瞳&s=2023-01-01&e=2024-12-31&n=10&source=invalid")
        assert response.status_code == 400

    @patch('backend.app.get_user_mid')
    def test_vtuber_not_found_error(self, mock_get_mid):
        """Test VTuber not found error handling"""
        mock_get_mid.side_effect = Exception("VTuber not found")
        
        response = client.get("/comment/video?vtuber=不存在的VTuber")
        assert response.status_code == 404

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
