"""
Integration tests for standardized RESTful APIs.

This module tests the actual API endpoints against a running server
to verify real-world functionality and response formats.
"""

import requests
import pytest
import json
from datetime import datetime
import time

# Configuration
BASE_URL = "http://localhost:9022"
TEST_VTUBER = "星瞳"  # Default test VTuber
TIMEOUT = 30  # Request timeout in seconds


class TestAPIIntegration:
    """Integration tests for standardized APIs"""
    
    @pytest.fixture(autouse=True)
    def setup(self):
        """Setup for each test"""
        self.base_url = BASE_URL
        self.headers = {"Content-Type": "application/json"}
        
        # Check if server is running
        try:
            response = requests.get(f"{self.base_url}/basic/mid", timeout=5)
            if response.status_code != 200:
                pytest.skip("Server not running or not responding")
        except requests.exceptions.RequestException:
            pytest.skip("Server not accessible")
    
    def test_dahanghai_rate_endpoint(self):
        """Test /basic/dahanghai/rate endpoint"""
        url = f"{self.base_url}/basic/dahanghai/rate"
        params = {"vtuber": TEST_VTUBER, "recent": 90}
        
        response = requests.get(url, params=params, timeout=TIMEOUT)
        
        # Check HTTP status
        assert response.status_code == 200, f"Expected 200, got {response.status_code}: {response.text}"
        
        # Check response format
        data = response.json()
        self._validate_success_response_format(data)
        
        # Check specific data structure
        response_data = data["data"]
        assert response_data["vtuber_name"] == TEST_VTUBER
        assert "mid" in response_data
        assert "growth_rate" in response_data
        assert "period_days" in response_data
        assert response_data["period_days"] == 90
        assert "timestamp" in response_data
        
        # Validate timestamp format
        self._validate_iso_timestamp(response_data["timestamp"])
    
    def test_follower_rate_endpoint(self):
        """Test /basic/follower/rate endpoint"""
        url = f"{self.base_url}/basic/follower/rate"
        params = {"vtuber": TEST_VTUBER, "recent": 30}
        
        response = requests.get(url, params=params, timeout=TIMEOUT)
        
        assert response.status_code == 200
        data = response.json()
        self._validate_success_response_format(data)
        
        response_data = data["data"]
        assert response_data["vtuber_name"] == TEST_VTUBER
        assert "growth_rate" in response_data
        assert response_data["period_days"] == 30
    
    def test_current_stat_endpoint(self):
        """Test /basic/stat/current endpoint"""
        url = f"{self.base_url}/basic/stat/current"
        params = {"vtuber": TEST_VTUBER}
        
        response = requests.get(url, params=params, timeout=TIMEOUT)
        
        assert response.status_code == 200
        data = response.json()
        self._validate_success_response_format(data)
        
        response_data = data["data"]
        assert response_data["vtuber_name"] == TEST_VTUBER
        assert "statistics" in response_data
        
        # Check statistics structure
        stats = response_data["statistics"]
        expected_fields = ["uid", "follower_num", "dahanghai_num", "video_total_num", 
                          "article_total_num", "likes_total_num", "elec_num"]
        for field in expected_fields:
            assert field in stats, f"Missing field {field} in statistics"
    
    def test_historical_stat_endpoint(self):
        """Test /basic/stat/all endpoint"""
        url = f"{self.base_url}/basic/stat/all"
        params = {"vtuber": TEST_VTUBER, "recent": 7}  # Last 7 days
        
        response = requests.get(url, params=params, timeout=TIMEOUT)
        
        assert response.status_code == 200
        data = response.json()
        self._validate_success_response_format(data)
        
        response_data = data["data"]
        assert response_data["vtuber_name"] == TEST_VTUBER
        assert response_data["period_days"] == 7
        assert "statistics" in response_data
        assert "total_records" in response_data
        assert isinstance(response_data["statistics"], list)
        
        # Check statistics structure if data exists
        if response_data["statistics"]:
            stat = response_data["statistics"][0]
            expected_fields = ["datetime", "video_total_num", "article_total_num", 
                              "likes_total_num", "elec_num", "follower_num", "dahanghai_num"]
            for field in expected_fields:
                assert field in stat, f"Missing field {field} in historical statistics"
    
    def test_medal_rank_endpoint(self):
        """Test /basic/medal_rank endpoint"""
        url = f"{self.base_url}/basic/medal_rank"
        params = {"vtuber": TEST_VTUBER}
        
        response = requests.get(url, params=params, timeout=TIMEOUT)
        
        # This might return 404 if no medal ranking data exists
        if response.status_code == 404:
            data = response.json()
            self._validate_error_response_format(data)
            assert data["detail"]["code"] == 404
            return
        
        assert response.status_code == 200
        data = response.json()
        self._validate_success_response_format(data)
        
        response_data = data["data"]
        assert response_data["vtuber_name"] == TEST_VTUBER
        assert "ranking" in response_data
        
        # Check ranking structure
        ranking = response_data["ranking"]
        assert "uid" in ranking
        assert "rank_list" in ranking
        assert isinstance(ranking["rank_list"], list)
    
    def test_parameter_validation(self):
        """Test parameter validation across endpoints"""
        endpoints = [
            "/basic/dahanghai/rate",
            "/basic/follower/rate",
            "/basic/stat/current",
            "/basic/stat/all",
            "/basic/medal_rank"
        ]
        
        for endpoint in endpoints:
            url = f"{self.base_url}{endpoint}"
            
            # Test empty vtuber parameter
            response = requests.get(url, params={"vtuber": ""}, timeout=TIMEOUT)
            assert response.status_code == 400, f"Endpoint {endpoint} should reject empty vtuber"
            
            data = response.json()
            self._validate_error_response_format(data)
            assert data["detail"]["code"] == 400
        
        # Test invalid recent parameter for endpoints that support it
        rate_endpoints = ["/basic/dahanghai/rate", "/basic/follower/rate", "/basic/stat/all"]
        for endpoint in rate_endpoints:
            url = f"{self.base_url}{endpoint}"
            response = requests.get(url, params={"vtuber": TEST_VTUBER, "recent": -5}, timeout=TIMEOUT)
            assert response.status_code == 400, f"Endpoint {endpoint} should reject invalid recent"
    
    def test_nonexistent_vtuber(self):
        """Test behavior with non-existent VTuber"""
        endpoints = [
            "/basic/dahanghai/rate",
            "/basic/follower/rate", 
            "/basic/stat/current",
            "/basic/stat/all",
            "/basic/medal_rank"
        ]
        
        fake_vtuber = "不存在的主播123"
        
        for endpoint in endpoints:
            url = f"{self.base_url}{endpoint}"
            response = requests.get(url, params={"vtuber": fake_vtuber}, timeout=TIMEOUT)
            
            assert response.status_code == 404, f"Endpoint {endpoint} should return 404 for non-existent VTuber"
            
            data = response.json()
            self._validate_error_response_format(data)
            assert data["detail"]["code"] == 404
            assert fake_vtuber in data["detail"]["message"]
    
    def test_default_parameters(self):
        """Test endpoints with default parameters"""
        # Test dahanghai rate with defaults
        response = requests.get(f"{self.base_url}/basic/dahanghai/rate", timeout=TIMEOUT)
        assert response.status_code == 200
        data = response.json()
        assert data["data"]["vtuber_name"] == "星瞳"  # Default vtuber
        assert data["data"]["period_days"] == 90  # Default recent
        
        # Test follower rate with defaults
        response = requests.get(f"{self.base_url}/basic/follower/rate", timeout=TIMEOUT)
        assert response.status_code == 200
        data = response.json()
        assert data["data"]["period_days"] == 90  # Default recent
    
    def test_response_time(self):
        """Test that API responses are reasonably fast"""
        endpoints = [
            "/basic/dahanghai/rate",
            "/basic/follower/rate",
            "/basic/stat/current",
            "/basic/medal_rank"
        ]
        
        for endpoint in endpoints:
            url = f"{self.base_url}{endpoint}"
            params = {"vtuber": TEST_VTUBER}
            
            start_time = time.time()
            response = requests.get(url, params=params, timeout=TIMEOUT)
            end_time = time.time()
            
            response_time = end_time - start_time
            
            # API should respond within 10 seconds
            assert response_time < 10, f"Endpoint {endpoint} took {response_time:.2f}s, too slow"
            
            # Should be successful or 404 (for endpoints that might not have data)
            assert response.status_code in [200, 404], f"Unexpected status for {endpoint}: {response.status_code}"
    
    def _validate_success_response_format(self, data):
        """Validate standard success response format"""
        assert isinstance(data, dict), "Response should be a dictionary"
        assert "code" in data, "Response missing 'code' field"
        assert "message" in data, "Response missing 'message' field"
        assert "data" in data, "Response missing 'data' field"
        
        assert data["code"] == 200, f"Expected code 200, got {data['code']}"
        assert isinstance(data["message"], str), "Message should be a string"
        assert isinstance(data["data"], dict), "Data should be a dictionary"
        
        # Check common data fields
        response_data = data["data"]
        assert "vtuber_name" in response_data, "Missing 'vtuber_name' in data"
        assert "mid" in response_data, "Missing 'mid' in data"
    
    def _validate_error_response_format(self, data):
        """Validate standard error response format"""
        assert isinstance(data, dict), "Error response should be a dictionary"
        assert "detail" in data, "Error response missing 'detail' field"
        
        detail = data["detail"]
        assert "code" in detail, "Error detail missing 'code' field"
        assert "message" in detail, "Error detail missing 'message' field"
        
        assert isinstance(detail["code"], int), "Error code should be an integer"
        assert isinstance(detail["message"], str), "Error message should be a string"
    
    def _validate_iso_timestamp(self, timestamp_str):
        """Validate ISO 8601 timestamp format"""
        try:
            datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
        except ValueError:
            pytest.fail(f"Invalid ISO timestamp format: {timestamp_str}")


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-s"])
