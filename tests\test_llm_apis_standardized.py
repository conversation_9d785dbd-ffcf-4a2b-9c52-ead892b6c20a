"""
Test suite for standardized LLM-related API endpoints.
Tests the new RESTful API interfaces for LLM functionality.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pytest
from unittest.mock import patch, AsyncMock
from fastapi.testclient import TestClient
from backend.app import app

client = TestClient(app)

class TestLLMAPIsStandardized:
    """Test class for standardized LLM API endpoints"""
    
    def setup_method(self):
        """Setup test data"""
        self.test_vtuber = "星瞳"
        self.test_mid = "401315430"
        self.test_recent = 30
        
        # Mock data for relationships
        self.mock_relationships = [
            "等一下，拯救者！中秋和扇宝一起拍'打歌视频'!嘎嘎~",
            "俏鸡传说原创专辑的第二首合唱曲《化身拯救者靠泡泡糖消灭黑暗怪兽》PV上线喽",
            "星瞳X扇宝的《倾城第一花》同名收藏集开启预约啦！"
        ]
        
        # Mock data for sentiment analysis
        self.mock_sentiment = {
            'love_ratio': 0.1,
            'positive_ratio': 0.2,
            'neutral_ratio': 0.3,
            'critical_ratio': 0.2,
            'negative_ratio': 0.2,
            'info': [
                {'time': '2023-01-01', 'comment': ['Great product!', 'Really liked it.'], 'sentiment': 0.85},
                {'time': '2023-01-02', 'comment': ['Not bad.', 'Could be better.'], 'sentiment': 0.55}
            ]
        }
        
        # Mock data for topics
        self.mock_topics = [
            {
                "topic": "虚拟主播星瞳'七杀梗'引发争议",
                "rank": 1,
                "heat": 5,
                "keywords": ["星瞳", "七杀梗", "背锅侠", "争议"],
                "comments": ["测试评论1", "测试评论2"]
            }
        ]

    @patch('backend.app.get_user_mid')
    @patch('backend.app.query_recent_relationships')
    def test_read_recent_relationships_success(self, mock_query, mock_get_mid):
        """Test successful relationships retrieval"""
        mock_get_mid.return_value = self.test_mid
        mock_query.return_value = '["关系1", "关系2", "关系3"]'
        
        response = client.get(f"/llm/relations?vtuber={self.test_vtuber}&recent={self.test_recent}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        assert data["message"] == "Recent relationships retrieved successfully"
        assert data["data"]["vtuber_name"] == self.test_vtuber
        assert data["data"]["mid"] == self.test_mid
        assert data["data"]["analysis_params"]["recent_days"] == self.test_recent
        assert len(data["data"]["relationships"]) == 3
        assert data["data"]["has_data"] == True

    @patch('backend.app.get_user_mid')
    @patch('backend.app.query_recent_comments_sentiment_value')
    def test_read_recent_sensiment_success(self, mock_query, mock_get_mid):
        """Test successful sentiment analysis retrieval"""
        mock_get_mid.return_value = self.test_mid
        mock_query.return_value = self.mock_sentiment
        
        response = client.get(f"/llm/sensiment/?vtuber={self.test_vtuber}&recent={self.test_recent}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        assert data["message"] == "Recent sentiment analysis retrieved successfully"
        assert data["data"]["vtuber_name"] == self.test_vtuber
        assert data["data"]["analysis_params"]["source"] == "all"
        assert "sentiment_analysis" in data["data"]
        assert "detailed_info" in data["data"]

    @patch('backend.app.get_user_mid')
    @patch('backend.app.query_recent_comments_sentiment_value')
    def test_read_recent_sensiment_bili_success(self, mock_query, mock_get_mid):
        """Test successful Bilibili sentiment analysis retrieval"""
        mock_get_mid.return_value = self.test_mid
        mock_query.return_value = self.mock_sentiment
        
        response = client.get(f"/llm/sensiment/bili?vtuber={self.test_vtuber}&recent={self.test_recent}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        assert data["message"] == "Recent Bilibili sentiment analysis retrieved successfully"
        assert data["data"]["analysis_params"]["source"] == "bili"

    @patch('backend.app.get_user_mid')
    @patch('backend.app.query_recent_comments_sentiment_value')
    def test_read_recent_sensiment_tieba_success(self, mock_query, mock_get_mid):
        """Test successful Tieba sentiment analysis retrieval"""
        mock_get_mid.return_value = self.test_mid
        mock_query.return_value = self.mock_sentiment
        
        response = client.get(f"/llm/sensiment/tieba/?vtuber={self.test_vtuber}&recent={self.test_recent}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        assert data["message"] == "Recent Tieba sentiment analysis retrieved successfully"
        assert data["data"]["analysis_params"]["source"] == "tieba"

    @patch('backend.app.get_user_mid')
    @patch('backend.app.query_tieba_summaries_from_ai_gen_table_by_date')
    def test_read_comment_topics_success(self, mock_query, mock_get_mid):
        """Test successful comment topics retrieval"""
        mock_get_mid.return_value = self.test_mid
        mock_query.return_value = self.mock_topics
        
        response = client.get(f"/llm/topics/?vtuber={self.test_vtuber}&recent={self.test_recent}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        assert data["message"] == "Comment topics retrieved successfully"
        assert data["data"]["vtuber_name"] == self.test_vtuber
        assert len(data["data"]["topics"]) == 1
        assert data["data"]["topics"][0]["topic"] == "虚拟主播星瞳'七杀梗'引发争议"

    @patch('backend.app.get_user_mid')
    @patch('backend.app.query_current_follower_change_num')
    def test_read_follower_arise_num_success(self, mock_query, mock_get_mid):
        """Test successful follower change retrieval"""
        mock_get_mid.return_value = self.test_mid
        mock_query.return_value = -20
        
        response = client.get(f"/basic/follower/rise/?vtuber={self.test_vtuber}&recent=1")
        
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        assert data["message"] == "Follower change retrieved successfully"
        assert data["data"]["follower_change"] == -20
        assert data["data"]["change_type"] == "decrease"

    @patch('backend.app.get_user_mid')
    @patch('backend.app.query_current_dahanghai_change_num')
    def test_read_dahanghai_arise_num_success(self, mock_query, mock_get_mid):
        """Test successful dahanghai change retrieval"""
        mock_get_mid.return_value = self.test_mid
        mock_query.return_value = 5
        
        response = client.get(f"/basic/dahanghai/rise/?vtuber={self.test_vtuber}&recent=1")
        
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        assert data["message"] == "Dahanghai change retrieved successfully"
        assert data["data"]["dahanghai_change"] == 5
        assert data["data"]["change_type"] == "increase"

    def test_parameter_validation_errors(self):
        """Test parameter validation for various endpoints"""
        # Test negative recent parameter
        response = client.get("/llm/relations?vtuber=星瞳&recent=-1")
        assert response.status_code == 400
        
        # Test excessive recent parameter
        response = client.get("/llm/sensiment/?vtuber=星瞳&recent=400")
        assert response.status_code == 400
        
        # Test empty vtuber parameter
        response = client.get("/llm/topics/?vtuber=&recent=30")
        assert response.status_code == 400

    @patch('backend.app.get_user_mid')
    def test_vtuber_not_found_error(self, mock_get_mid):
        """Test VTuber not found error handling"""
        mock_get_mid.side_effect = Exception("VTuber not found")
        
        response = client.get("/llm/relations?vtuber=不存在的VTuber&recent=30")
        assert response.status_code == 404

    @patch('backend.app.get_user_mid')
    @patch('backend.app.query_recent_comments_sentiment_value')
    def test_no_data_found_error(self, mock_query, mock_get_mid):
        """Test no data found error handling"""
        mock_get_mid.return_value = self.test_mid
        mock_query.return_value = None
        
        response = client.get("/llm/sensiment/?vtuber=星瞳&recent=30")
        assert response.status_code == 404

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
