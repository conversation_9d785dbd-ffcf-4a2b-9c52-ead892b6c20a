"""
Verification script to check if the RESTful standardization was applied correctly.
This script analyzes the code changes without importing the modules.
"""

import re
import os

def check_function_standardization(file_path, function_name, start_line):
    """Check if a function has been properly standardized"""
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # Find the function
    function_found = False
    function_lines = []
    brace_count = 0
    in_function = False
    
    for i, line in enumerate(lines[start_line-1:], start_line):
        if f"async def {function_name}" in line:
            function_found = True
            in_function = True
            function_lines.append((i, line))
            continue
        
        if in_function:
            function_lines.append((i, line))
            
            # Simple way to detect function end - look for next function or class
            if line.strip().startswith('@app.') or line.strip().startswith('class ') or line.strip().startswith('def '):
                if not line.strip().startswith(f'async def {function_name}'):
                    break
    
    if not function_found:
        return False, f"Function {function_name} not found"
    
    # Check for standardization indicators
    function_text = ''.join([line for _, line in function_lines])
    
    checks = {
        'has_try_catch': 'try:' in function_text and 'except' in function_text,
        'has_parameter_validation': 'Parameter validation' in function_text or 'if not vtuber' in function_text,
        'has_create_success_response': 'create_success_response' in function_text,
        'has_create_error_response': 'create_error_response' in function_text,
        'has_http_exception': 'HTTPException' in function_text,
        'has_standard_docstring': 'Standard API response' in function_text,
        'has_timestamp': 'timestamp' in function_text or 'retrieved_at' in function_text,
    }
    
    return True, checks

def main():
    """Main verification function"""
    print("RESTful API Standardization Verification")
    print("=" * 50)
    
    app_file = "backend/app.py"
    
    if not os.path.exists(app_file):
        print(f"❌ File {app_file} not found")
        return
    
    # Functions we standardized and their approximate line numbers
    functions_to_check = [
        ("read_basic_dahanghai_rate", 766),
        ("read_basic_follower_rate", 843),
        ("read_basic_current_stat", 920),
        ("read_basic_all_stat", 1069),
        ("read_basic_medal_rank", 1173),
        ("read_basic_medal_rank_target", 1275),
        ("read_basic_info", 1406),
        ("read_live_info", 1492),
        ("read_dynamics_list", 1595),
        ("read_current_dynamics", 1836),
        ("read_video_list", 1939),
        ("read_video_top_n", 2060),
    ]
    
    print(f"Checking {len(functions_to_check)} standardized functions...\n")
    
    all_passed = True
    
    for func_name, line_num in functions_to_check:
        print(f"Checking {func_name}:")
        
        found, result = check_function_standardization(app_file, func_name, line_num)
        
        if not found:
            print(f"  ❌ {result}")
            all_passed = False
            continue
        
        # Check each standardization aspect
        checks = result
        for check_name, passed in checks.items():
            status = "✅" if passed else "❌"
            print(f"  {status} {check_name.replace('_', ' ').title()}")
            if not passed:
                all_passed = False
        
        print()
    
    # Check for response format functions
    print("Checking response format functions:")
    with open(app_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    response_functions = [
        'create_success_response',
        'create_error_response'
    ]
    
    for func in response_functions:
        if f"def {func}" in content:
            print(f"  ✅ {func} function exists")
        else:
            print(f"  ❌ {func} function missing")
            all_passed = False
    
    print()
    
    # Check for imports
    print("Checking required imports:")
    required_imports = [
        'from fastapi import FastAPI, HTTPException, status',
        'from datetime import datetime',
        'from typing import Optional'
    ]
    
    for import_stmt in required_imports:
        if import_stmt in content:
            print(f"  ✅ {import_stmt}")
        else:
            print(f"  ❌ Missing: {import_stmt}")
    
    print()
    
    # Summary
    if all_passed:
        print("🎉 All standardization checks passed!")
        print("\nStandardization Summary:")
        print("- ✅ Unified response format implemented")
        print("- ✅ Parameter validation added")
        print("- ✅ Error handling standardized")
        print("- ✅ HTTP status codes properly used")
        print("- ✅ Documentation updated")
        print("- ✅ Timestamp formatting added")
    else:
        print("⚠️  Some standardization checks failed.")
        print("Please review the failed items above.")
    
    print("\nNext steps:")
    print("1. Restart the backend server to apply changes")
    print("2. Test the APIs with the new standardized format")
    print("3. Update frontend code to handle new response format")

if __name__ == "__main__":
    main()
