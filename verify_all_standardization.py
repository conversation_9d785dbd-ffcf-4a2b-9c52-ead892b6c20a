"""
Comprehensive verification script for all standardized RESTful APIs.
Checks all 19 standardized interfaces for compliance with RESTful standards.
"""

import ast
import re
from pathlib import Path

def check_function_standardization(func_code, func_name):
    """Check if a function follows standardization patterns"""
    checks = {
        'has_try_catch': False,
        'has_parameter_validation': False,
        'has_create_success_response': False,
        'has_create_error_response': False,
        'has_http_exception': False,
        'has_standard_docstring': False,
        'has_timestamp': False
    }
    
    # Check for try-catch blocks
    if 'try:' in func_code and 'except' in func_code:
        checks['has_try_catch'] = True
    
    # Check for parameter validation
    validation_patterns = [
        r'if not .+ or not .+\.strip\(\):',
        r'raise HTTPException.*400',
        r'Parameter .+ cannot be empty'
    ]
    for pattern in validation_patterns:
        if re.search(pattern, func_code):
            checks['has_parameter_validation'] = True
            break
    
    # Check for success response creation
    if 'create_success_response' in func_code:
        checks['has_create_success_response'] = True
    
    # Check for error response creation
    if 'create_error_response' in func_code:
        checks['has_create_error_response'] = True
    
    # Check for HTTP exceptions
    if 'HTTPException' in func_code:
        checks['has_http_exception'] = True
    
    # Check for standard English docstring
    docstring_match = re.search(r'"""(.*?)"""', func_code, re.DOTALL)
    if docstring_match:
        docstring = docstring_match.group(1)
        if any(keyword in docstring for keyword in ['Args:', 'Returns:', 'Example:']):
            checks['has_standard_docstring'] = True
    
    # Check for timestamp
    if 'datetime.now().isoformat()' in func_code or 'retrieved_at' in func_code:
        checks['has_timestamp'] = True
    
    return checks

def verify_all_standardization():
    """Verify standardization of all API functions"""
    
    # Complete list of all standardized functions
    all_functions = {
        "Basic Data APIs": [
            'read_basic_dahanghai_rate',
            'read_basic_follower_rate',
            'read_basic_current_stat',
            'read_basic_all_stat',
            'read_basic_medal_rank'
        ],
        "User Info APIs": [
            'read_basic_medal_rank_target',
            'read_basic_info'
        ],
        "Content APIs": [
            'read_live_info',
            'read_dynamics_list',
            'read_current_dynamics',
            'read_video_list',
            'read_video_top_n'
        ],
        "Comment Analysis APIs": [
            'read_comment_video',
            'read_comment_dynamic',
            'read_comment_top_n',
            'read_comment_top_n_user',
            'read_comment_wordcloud'
        ],
        "Forum APIs": [
            'read_whole_tieba',
            'read_thread_tieba'
        ],
        "AI Analysis APIs": [
            'read_recent_relationships',
            'read_recent_sensiment',
            'read_recent_sensiment_bili',
            'read_recent_sensiment_tieba',
            'read_comment_topics',
            'read_follower_arise_num',
            'read_dahanghai_arise_num'
        ],
        "Video Analysis APIs": [
            'read_recent_video_day_views',
            'read_target_video_day_views',
            'read_top_n_video_day_by_time',
            'read_top_n_video_day_by_period',
            'read_target_dahanghai_whole_list',
            'read_recent_info'
        ]
    }
    
    app_file = Path('backend/app.py')
    if not app_file.exists():
        print("❌ backend/app.py file not found!")
        return False
    
    with open(app_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("🔍 Comprehensive API Standardization Verification")
    print("=" * 70)
    print("Verifying all 32 standardized API endpoints...")
    print()
    
    all_passed = True
    total_functions = 0
    passed_functions = 0
    category_results = {}
    
    for category, functions in all_functions.items():
        print(f"\n📂 {category}")
        print("-" * 50)
        
        category_passed = 0
        category_total = len(functions)
        total_functions += category_total
        
        for func_name in functions:
            print(f"\n📡 Checking {func_name}...")
            
            # Extract function code
            pattern = rf'async def {func_name}\(.*?\n(?:(?!async def|@app\.|class ).)*'
            match = re.search(pattern, content, re.DOTALL)
            
            if not match:
                print(f"   ❌ Function {func_name} not found")
                all_passed = False
                continue
            
            func_code = match.group(0)
            checks = check_function_standardization(func_code, func_name)
            
            # Display results
            function_passed = True
            for check_name, passed in checks.items():
                status = "✅" if passed else "❌"
                check_display = check_name.replace('_', ' ').title()
                print(f"   {status} {check_display}")
                if not passed:
                    function_passed = False
                    all_passed = False
            
            if function_passed:
                category_passed += 1
                passed_functions += 1
                print(f"   ✅ {func_name} - All checks passed!")
            else:
                print(f"   ❌ {func_name} - Some checks failed")
        
        category_results[category] = (category_passed, category_total)
        success_rate = category_passed / category_total * 100
        print(f"\n📊 {category} Summary: {category_passed}/{category_total} ({success_rate:.1f}%)")
    
    print("\n" + "=" * 70)
    print("📋 Overall Standardization Results:")
    print(f"   • Total functions checked: {total_functions}")
    print(f"   • Functions passed: {passed_functions}")
    print(f"   • Overall success rate: {passed_functions/total_functions*100:.1f}%")
    
    print("\n📊 Category Breakdown:")
    for category, (passed, total) in category_results.items():
        status = "✅" if passed == total else "⚠️"
        print(f"   {status} {category}: {passed}/{total}")
    
    if all_passed:
        print("\n🎉 All API functions are properly standardized!")
        print("\n✨ Standardization Features Verified:")
        print("   • ✅ Unified response format implemented")
        print("   • ✅ Parameter validation added")
        print("   • ✅ Error handling standardized")
        print("   • ✅ HTTP status codes properly used")
        print("   • ✅ Documentation updated")
        print("   • ✅ Timestamp formatting added")
        print("   • ✅ Try-catch blocks implemented")
        
        print("\n🏆 Achievement Unlocked:")
        print("   • 19 APIs successfully standardized")
        print("   • 100% RESTful compliance achieved")
        print("   • Production-ready API interface")
        print("   • Consistent developer experience")
    else:
        print(f"\n⚠️  {total_functions - passed_functions} functions need attention")
    
    return all_passed

def check_response_format_consistency():
    """Check if all functions use consistent response format"""
    print("\n🔍 Checking Response Format Consistency")
    print("-" * 50)
    
    app_file = Path('backend/app.py')
    with open(app_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check for consistent response structure patterns
    response_patterns = {
        'Success Response': r'create_success_response\(',
        'VTuber Name': r'"vtuber_name":\s*vtuber',
        'MID Field': r'"mid":\s*mid',
        'Timestamp': r'"retrieved_at":\s*datetime\.now\(\)\.isoformat\(\)',
        'Total Count': r'"total_count":\s*len\(',
        'Error Response': r'create_error_response\(',
        'HTTP Exception': r'HTTPException\(',
        'Parameter Validation': r'if not .+ or not .+\.strip\(\):'
    }
    
    consistency_results = {}
    for pattern_name, pattern in response_patterns.items():
        matches = len(re.findall(pattern, content))
        consistency_results[pattern_name] = matches
        expected_min = 15 if pattern_name in ['Success Response', 'Error Response', 'HTTP Exception'] else 10
        status = "✅" if matches >= expected_min else "⚠️"
        print(f"   {status} {pattern_name}: {matches} occurrences")
    
    consistent_patterns = sum(1 for count in consistency_results.values() if count >= 10)
    total_patterns = len(response_patterns)
    
    print(f"\n📊 Response Format Consistency: {consistent_patterns}/{total_patterns} patterns consistent")
    return consistent_patterns >= total_patterns * 0.8  # 80% threshold

def main():
    """Main verification function"""
    print("🚀 Complete RESTful API Standardization Verification")
    print("=" * 70)
    print("Verifying all 19 standardized API endpoints...")
    print()
    
    # Verify standardization
    standardization_passed = verify_all_standardization()
    
    # Check response format consistency
    consistency_passed = check_response_format_consistency()
    
    print("\n" + "=" * 70)
    print("🏁 Final Verification Results:")
    print(f"   • API Standardization: {'✅ PASSED' if standardization_passed else '❌ FAILED'}")
    print(f"   • Response Consistency: {'✅ PASSED' if consistency_passed else '❌ FAILED'}")
    
    if standardization_passed and consistency_passed:
        print("\n🎉 CONGRATULATIONS! All APIs are successfully standardized!")
        print("   🚀 Ready for production deployment")
        print("   📚 Comprehensive documentation available")
        print("   🔧 Consistent developer experience")
        print("   🛡️  Robust error handling implemented")
        print("   📊 32 endpoints following RESTful best practices")
    else:
        print("\n⚠️  Some issues found. Please review and fix before deployment.")
    
    return standardization_passed and consistency_passed

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
